from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QPushButton,
                             QHBoxLayout, QFrame, QSizePolicy)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QFont
from resource_path import resource_path

class AboutDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Sobre o Lead Finder")
        self.setFixedSize(500, 400)
        self.setWindowFlag(Qt.WindowType.WindowContextHelpButtonHint, False)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Header with logo
        header_layout = QHBoxLayout()

        # Logo
        logo_label = QLabel()
        pixmap = QPixmap(resource_path('finder.png'))
        scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)
        logo_label.setPixmap(scaled_pixmap)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(logo_label)

        # App info
        info_layout = QVBoxLayout()

        app_name = QLabel("Lead Finder")
        app_name.setFont(QFont('Segoe UI', 16, QFont.Weight.Bold))
        info_layout.addWidget(app_name)

        version = QLabel("Versão 1.0.0")
        version.setFont(QFont('Segoe UI', 10))
        info_layout.addWidget(version)

        header_layout.addLayout(info_layout)
        header_layout.addStretch()

        main_layout.addLayout(header_layout)

        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(separator)

        # Description
        description = QLabel(
            "Lead Finder é uma ferramenta para captura automatizada de leads do Google Maps. "
            "Permite buscar por empresas em uma determinada região e extrair informações "
            "como nome, telefone, endereço e site."
        )
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignmentFlag.AlignJustify)
        main_layout.addWidget(description)

        # Features
        features_title = QLabel("Principais recursos:")
        features_title.setFont(QFont('Segoe UI', 10, QFont.Weight.Bold))
        main_layout.addWidget(features_title)

        features = QLabel(
            "• Busca por CEP e palavra-chave\n"
            "• Extração de múltiplos leads em uma única operação\n"
            "• Exportação para Excel\n"
            "• Interface amigável e intuitiva"
        )
        features.setIndent(10)
        main_layout.addWidget(features)

        # Credits
        credits = QLabel("© 2025 - Todos os direitos reservados")
        credits.setAlignment(Qt.AlignmentFlag.AlignCenter)
        credits.setFont(QFont('Segoe UI', 8))
        main_layout.addWidget(credits)

        main_layout.addStretch()

        # Close button
        close_button = QPushButton("Fechar")
        close_button.clicked.connect(self.accept)
        close_button.setFixedWidth(100)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        main_layout.addLayout(button_layout)

        # Apply stylesheet
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #212529;
            }
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            QFrame {
                color: #dee2e6;
            }
        """)

if __name__ == "__main__":
    # Test the dialog
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    dialog = AboutDialog()
    dialog.exec()
