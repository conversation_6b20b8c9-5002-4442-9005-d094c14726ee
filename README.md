# Lead Finder

Ferramenta para captura automatizada de leads do Google Maps.

## Requisitos

- Python 3.8 ou superior
- Bibliotecas listadas em `requirements.txt`

## Instalação das dependências

```
pip install -r requirements.txt
```

## Executando o programa (modo de desenvolvimento)

```
python ui.py
```

## Compilando para executável Windows (.exe)

### Método 1: Usando o script de compilação segura

Este método é recomendado para criar um executável que minimize falsos positivos em antivírus:

1. Execute o script de compilação segura:

```
python build_safe_exe.py
```

2. O script irá:
   - Verificar e instalar dependências necessárias
   - Limpar builds anteriores
   - Compilar o executável com configurações otimizadas
   - Verificar o build resultante

3. O executável será criado na pasta `dist/`

### Método 2: Usando auto-py-to-exe

Para uma interface gráfica de compilação:

1. Execute o script auxiliar:

```
python compile_gui.py
```

2. <PERSON><PERSON> as instruções na interface do auto-py-to-exe:
   - Em 'Script Location' selecione o arquivo ui.py
   - Selecione 'One File' e 'Window Based (hide the console)'
   - Em 'Icon' selecione o arquivo finder.png
   - Em 'Additional Files' adicione finder.png
   - Em 'Version File' selecione file_version_info.txt
   - Clique em 'CONVERT .PY TO .EXE'

## Notas importantes

- O Chrome Driver é baixado automaticamente pela aplicação
- É necessário ter o Google Chrome instalado no computador
- O arquivo `finder.png` deve estar presente no mesmo diretório que o executável

## Funcionalidades

- Captura de leads do Google Maps por área geográfica (CEP)
- Busca por palavra-chave
- Exportação de dados para Excel
- Interface gráfica intuitiva

## Troubleshooting

Se ocorrer algum erro durante a execução:

1. Verifique se todas as dependências estão instaladas
2. Certifique-se de que o Google Chrome está atualizado
3. Verifique os logs de erro na aplicação

## Reduzindo Falsos Positivos em Antivírus

Se o executável for detectado como falso positivo por antivírus:

1. **Adicione exceções no antivírus**:
   - Adicione o executável à lista de exceções do seu antivírus
   - Adicione a pasta onde o executável está localizado às exceções

2. **Use o script de compilação segura**:
   - O script `build_safe_exe.py` usa configurações otimizadas para reduzir falsos positivos

3. **Assine digitalmente o executável**:
   - A assinatura digital pode reduzir significativamente os falsos positivos
   - Requer um certificado de assinatura de código (pago)

4. **Reporte o falso positivo**:
   - Contate o suporte do antivírus para reportar o falso positivo

## Licença

Copyright © 2023-2024 Lead Capture Tools. Todos os direitos reservados.